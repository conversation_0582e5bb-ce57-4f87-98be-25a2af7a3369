import React, { useState } from 'react';
import { commentService } from '../services/commentService';
import { Send, MessageSquare } from 'lucide-react';
import toast from 'react-hot-toast';

const CommentForm = ({ onCommentSubmitted }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!content.trim()) {
      toast.error('Please enter a comment');
      return;
    }

    if (content.length > 500) {
      toast.error('Comment must be less than 500 characters');
      return;
    }

    try {
      setLoading(true);
      const response = await commentService.createComment(content.trim());
      
      if (onCommentSubmitted) {
        onCommentSubmitted(response.data);
      }
      
      setContent('');
      
      // Show different messages based on comment status
      const status = response.data.status;
      if (status === 'allowed') {
        toast.success('Comment posted successfully!');
      } else if (status === 'flagged') {
        toast.warning('Comment submitted for review');
      } else if (status === 'removed') {
        toast.error('Comment was blocked due to policy violations');
      }
      
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to submit comment';
      toast.error(errorMessage);
      console.error('Error submitting comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const characterCount = content.length;
  const isOverLimit = characterCount > 500;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="comment" className="block text-sm font-medium text-gray-300 mb-2">
          Your Comment
        </label>
        <div className="relative">
          <textarea
            id="comment"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Share your thoughts... (AI will analyze sentiment and toxicity)"
            rows={4}
            className="block w-full px-4 py-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
            disabled={loading}
          />
          <div className="absolute bottom-3 right-3 flex items-center space-x-2">
            <MessageSquare className="w-4 h-4 text-gray-400" />
            <span className={`text-xs ${isOverLimit ? 'text-red-400' : 'text-gray-400'}`}>
              {characterCount}/500
            </span>
          </div>
        </div>
        
        {isOverLimit && (
          <p className="text-red-400 text-sm mt-1">
            Comment exceeds maximum length of 500 characters
          </p>
        )}
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-400">
          <p>💡 Tips for better engagement:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Be respectful and constructive</li>
            <li>Stay on topic</li>
            <li>Avoid offensive language</li>
          </ul>
        </div>
        
        <button
          type="submit"
          disabled={loading || !content.trim() || isOverLimit}
          className="flex items-center space-x-2 btn-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Submitting...</span>
            </>
          ) : (
            <>
              <Send className="w-4 h-4" />
              <span>Submit Comment</span>
            </>
          )}
        </button>
      </div>

      {/* AI Analysis Info */}
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-300 mb-2">🤖 AI Analysis</h4>
        <p className="text-xs text-gray-300">
          Your comment will be automatically analyzed for sentiment and toxicity. 
          Comments that pass our community guidelines will be posted immediately, 
          while others may be flagged for review.
        </p>
      </div>
    </form>
  );
};

export default CommentForm;
