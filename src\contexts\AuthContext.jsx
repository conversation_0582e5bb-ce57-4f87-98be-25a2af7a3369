import { createContext, useContext, useReducer, useEffect } from 'react';
import { authService } from '../services/authService';

const AuthContext = createContext();

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true, error: null };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        loading: false,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        loading: false,
        isAuthenticated: false,
        user: null,
        token: null,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        error: null,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuthStatus = async () => {
      const user = localStorage.getItem('user');

      console.log('AuthContext - Initial load - user:', user); // Debug log

      if (user) {
        try {
          const parsedUser = JSON.parse(user);
          console.log('AuthContext - Checking if session is still valid for user:', parsedUser); // Debug log

          // Verify the session is still valid by making a request to the backend
          try {
            const response = await authService.getProfile();
            console.log('AuthContext - Session valid, user profile:', response.data); // Debug log
            dispatch({
              type: 'LOGIN_SUCCESS',
              payload: { token: 'cookie-based', user: response.data.user || response.data },
            });
          } catch (error) {
            console.log('AuthContext - Session invalid, clearing user data:', error); // Debug log
            localStorage.removeItem('user');
            dispatch({ type: 'LOGOUT' });
          }
        } catch (error) {
          console.log('AuthContext - Error parsing stored user, clearing storage:', error); // Debug log
          localStorage.removeItem('user');
          dispatch({ type: 'LOGOUT' });
        }
      } else {
        console.log('AuthContext - No stored user found'); // Debug log
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (email, password) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const response = await authService.login(email, password);
      console.log('Login response:', response.data); // Debug log

      // With cookie-based auth, we don't need to extract and store the token
      // The cookie is automatically set by the backend and sent with future requests
      const user = response.data.user || response.data;

      console.log('Extracted user:', user); // Debug log

      // Only store user data, not the token (since it's in cookies)
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { token: 'cookie-based', user }, // Use placeholder for token
      });

      return { success: true };
    } catch (error) {
      console.error('Login error:', error); // Debug log
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  const register = async (userData) => {
    dispatch({ type: 'LOGIN_START' });
    try {
      const response = await authService.register(userData);
      console.log('Register response:', response.data); // Debug log

      // With cookie-based auth, we don't need to extract and store the token
      const user = response.data.user || response.data;

      console.log('Extracted user:', user); // Debug log

      // Only store user data, not the token (since it's in cookies)
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { token: 'cookie-based', user }, // Use placeholder for token
      });

      return { success: true };
    } catch (error) {
      console.error('Register error:', error); // Debug log
      const errorMessage = error.response?.data?.message || 'Registration failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      // Call backend logout to clear the cookie
      await authService.logout();
    } catch (error) {
      console.log('Logout error (continuing anyway):', error);
    }

    // Clear local user data
    localStorage.removeItem('user');
    dispatch({ type: 'LOGOUT' });
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
