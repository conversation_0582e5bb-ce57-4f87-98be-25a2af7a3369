import React, { useState, useEffect } from 'react';
import { commentService } from '../services/commentService';
import CommentTable from '../components/CommentTable';
import { Shield, AlertTriangle, CheckCircle, XCircle, BarChart3 } from 'lucide-react';
import toast from 'react-hot-toast';
import logo from '../assets/logo.png';

const AdminPanel = () => {
  const [activeTab, setActiveTab] = useState('flagged');
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    flagged: 0,
    allowed: 0,
    removed: 0,
  });

  const tabs = [
    {
      id: 'flagged',
      label: 'Flagged',
      icon: AlertTriangle,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-600',
    },
    {
      id: 'removed',
      label: 'Removed',
      icon: XCircle,
      color: 'text-red-400',
      bgColor: 'bg-red-600',
    },
    {
      id: 'allowed',
      label: 'Allowed',
      icon: CheckCircle,
      color: 'text-green-400',
      bgColor: 'bg-green-600',
    },
  ];

  const fetchComments = async (status) => {
    try {
      setLoading(true);
      const response = await commentService.getComments(status);
      setComments(response.data.comments || []);
      setStats(response.data.stats || stats);
    } catch (error) {
      toast.error(`Failed to fetch ${status} comments`);
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await commentService.getCommentStats();
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchComments(activeTab);
    fetchStats();
  }, [activeTab]);

  const handleCommentAction = async (commentId, action) => {
    try {
      if (action === 'approve') {
        await commentService.allowComment(commentId);
        toast.success('Comment approved successfully');
      } else if (action === 'remove') {
        await commentService.removeComment(commentId);
        toast.success('Comment removed successfully');
      }

      // Remove the comment from the current list
      setComments(prev => prev.filter(comment => comment.id !== commentId));

      // Update stats based on action and current tab
      setStats(prev => {
        const newStats = { ...prev };

        if (action === 'approve') {
          newStats.allowed = prev.allowed + 1;
          if (activeTab === 'flagged') {
            newStats.flagged = prev.flagged - 1;
          } else if (activeTab === 'removed') {
            newStats.removed = prev.removed - 1;
          }
        } else if (action === 'remove') {
          newStats.removed = prev.removed + 1;
          if (activeTab === 'flagged') {
            newStats.flagged = prev.flagged - 1;
          }
        }

        return newStats;
      });

    } catch (error) {
      const errorMessage = error.response?.data?.message || `Failed to ${action} comment`;
      toast.error(errorMessage);
      console.error(`Error ${action}ing comment:`, error);
    }
  };

  const statCards = [
    {
      label: 'Total Comments',
      value: stats.total,
      icon: BarChart3,
      color: 'text-blue-400',
    },
    {
      label: 'Flagged',
      value: stats.flagged,
      icon: AlertTriangle,
      color: 'text-yellow-400',
    },
    {
      label: 'Allowed',
      value: stats.allowed,
      icon: CheckCircle,
      color: 'text-green-400',
    },
    {
      label: 'Removed',
      value: stats.removed,
      icon: XCircle,
      color: 'text-red-400',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4 group">
          <div className="w-16 h-16 bg-gradient-to-br from-red-500/20 to-orange-600/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-3 p-2 border border-white/20 shadow-lg">
            <img
              src={logo}
              alt="SENTINEL AI Logo"
              className="w-full h-full object-contain drop-shadow-lg"
            />
          </div>
          <h1 className="text-4xl font-bold sentinel-text group-hover:scale-105 transition-transform duration-200">
            SENTINEL Command Center
          </h1>
        </div>
        <p className="text-xl text-gray-300">
          Advanced AI-powered content moderation and community protection
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <div key={index} className="glass rounded-lg p-6 text-center">
            <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-3`} />
            <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
            <div className="text-sm text-gray-400">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Tabs */}
      <div className="glass rounded-lg p-6">
        <div className="flex flex-wrap gap-2 mb-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? `${tab.bgColor} text-white`
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
              <span className="bg-black/20 text-xs px-2 py-1 rounded-full">
                {stats[tab.id] || 0}
              </span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="min-h-[400px]">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <CommentTable
              comments={comments}
              status={activeTab}
              onAction={handleCommentAction}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;
