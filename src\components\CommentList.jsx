import React from 'react';
import CommentCard from './CommentCard';
import { MessageSquare } from 'lucide-react';

const CommentList = ({ comments = [] }) => {
  if (comments.length === 0) {
    return (
      <div className="text-center py-12">
        <MessageSquare className="w-16 h-16 text-gray-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-400 mb-2">No comments yet</h3>
        <p className="text-gray-500">
          Be the first to share your thoughts!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map((comment) => (
        <CommentCard key={comment.id} comment={comment} />
      ))}
    </div>
  );
};

export default CommentList;
