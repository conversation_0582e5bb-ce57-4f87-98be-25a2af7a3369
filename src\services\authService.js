import api from './api';

export const authService = {
  // Login user
  login: (email, password) => {
    return api.post('/auth/login', { email, password });
  },

  // Register user
  register: (userData) => {
    // Ensure the userData includes all required fields: name, email, password, role
    const registrationData = {
      name: userData.name,
      email: userData.email,
      password: userData.password,
      role: userData.role || 'user' // Default to 'user' if no role specified
    };
    return api.post('/auth/register', registrationData);
  },

  // Logout user
  logout: () => {
    return api.post('/auth/logout');
  },

  // Get current user profile
  getProfile: () => {
    return api.get('/auth/profile');
  },

  // Refresh token
  refreshToken: () => {
    return api.post('/auth/refresh');
  },
};
