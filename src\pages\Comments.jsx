import React, { useState, useEffect } from 'react';
import { commentService } from '../services/commentService';
import { useAuth } from '../contexts/AuthContext';
import CommentForm from '../components/CommentForm';
import CommentList from '../components/CommentList';
import { MessageSquare, TrendingUp, Users, CheckCircle, Clock, X } from 'lucide-react';
import toast from 'react-hot-toast';

const Comments = () => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    allowed: 0,
    flagged: 0,
    removed: 0,
  });
  const { user } = useAuth();

  const fetchComments = async () => {
    try {
      setLoading(true);
      let response;

      // Use different endpoints based on user role
      if (user?.role === 'admin') {
        // Admin sees all allowed comments
        response = await commentService.getComments('allowed');
      } else {
        // Normal users see their own posts (all statuses)
        response = await commentService.getUserPosts();
      }

      setComments(response.data.comments || []);
      setStats(response.data.stats || stats);
    } catch (error) {
      toast.error('Failed to fetch comments');
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, []);

  const handleCommentSubmitted = (newComment) => {
    // Add the new comment to the list if it's allowed
    if (newComment.status === 'allowed') {
      setComments(prev => [newComment, ...prev]);
    }
    
    // Update stats
    setStats(prev => ({
      ...prev,
      total: prev.total + 1,
      [newComment.status]: prev[newComment.status] + 1,
    }));
    
    toast.success('Comment submitted successfully!');
  };

  const statCards = user?.role === 'admin' ? [
    {
      label: 'Total Comments',
      value: stats.total,
      icon: MessageSquare,
      color: 'text-blue-400',
    },
    {
      label: 'Allowed',
      value: stats.allowed,
      icon: CheckCircle,
      color: 'text-green-400',
    },
    {
      label: 'Community Users',
      value: new Set(comments.map(c => c.author?.id)).size,
      icon: Users,
      color: 'text-purple-400',
    },
    {
      label: 'Engagement',
      value: '95%',
      icon: TrendingUp,
      color: 'text-yellow-400',
    },
  ] : [
    {
      label: 'My Posts',
      value: stats.total,
      icon: MessageSquare,
      color: 'text-blue-400',
    },
    {
      label: 'Approved',
      value: stats.allowed,
      icon: CheckCircle,
      color: 'text-green-400',
    },
    {
      label: 'Pending',
      value: stats.flagged,
      icon: Clock,
      color: 'text-yellow-400',
    },
    {
      label: 'Removed',
      value: stats.removed,
      icon: X,
      color: 'text-red-400',
    },
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">
          {user?.role === 'admin' ? 'Community Comments' : 'My Comments'}
        </h1>
        <p className="text-xl text-gray-300">
          {user?.role === 'admin'
            ? 'View all approved community comments'
            : 'Manage your posts and share your thoughts'
          }
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {statCards.map((stat, index) => (
          <div key={index} className="glass rounded-lg p-4 text-center">
            <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-sm text-gray-400">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Comment Form */}
      <div className="glass rounded-lg p-6">
        <h2 className="text-2xl font-semibold text-white mb-4 flex items-center">
          <MessageSquare className="w-6 h-6 mr-2" />
          Share Your Thoughts
        </h2>
        <CommentForm onCommentSubmitted={handleCommentSubmitted} />
      </div>

      {/* Comments List */}
      <div className="glass rounded-lg p-6">
        <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
          <TrendingUp className="w-6 h-6 mr-2" />
          {user?.role === 'admin' ? 'Recent Comments' : 'My Posts'}
        </h2>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <CommentList comments={comments} />
        )}
      </div>
    </div>
  );
};

export default Comments;
