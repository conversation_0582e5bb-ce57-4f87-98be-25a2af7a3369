import api from './api';

export const commentService = {
  // Get posts for normal users (their own posts)
  getUserPosts: async (status = null, page = 1, limit = 10) => {
    try {
      console.log('Fetching user posts from /posts/my-posts'); // Debug log
      const response = await api.get('/posts/my-posts');
      // Transform the response to match the expected format
      const rawPosts = response.data || [];

      console.log('Raw user posts response:', rawPosts); // Debug log

      // Transform posts to match frontend expectations
      const posts = rawPosts.map(post => ({
        id: post._id,
        content: post.content,
        author: {
          id: post.userId._id,
          name: post.userId.name,
          email: post.userId.email
        },
        toxicity: post.toxicity,
        sentiment: post.sentiment,
        finalDecision: post.finalDecision,
        status: post.finalDecision, // Add status field for compatibility
        createdAt: post.createdAt,
        updatedAt: post.updatedAt || post.createdAt
      }));

      // Filter posts by status if provided
      let filteredPosts = posts;
      if (status) {
        filteredPosts = posts.filter(post => {
          if (status === 'allowed') return post.finalDecision === 'allowed';
          if (status === 'flagged') return post.finalDecision === 'flagged';
          if (status === 'removed') return post.finalDecision === 'removed';
          return true;
        });
      }

      console.log('Transformed user posts:', filteredPosts); // Debug log

      return {
        data: {
          comments: filteredPosts,
          stats: {
            total: posts.length,
            allowed: posts.filter(p => p.finalDecision === 'allowed').length,
            flagged: posts.filter(p => p.finalDecision === 'flagged').length,
            removed: posts.filter(p => p.finalDecision === 'removed').length,
          }
        }
      };
    } catch (error) {
      console.error('Error fetching user posts:', error);
      throw error;
    }
  },

  // Get posts for admin (all posts)
  getComments: async (status = null, page = 1, limit = 10) => {
    try {
      console.log('Fetching admin posts from /posts/getPosts'); // Debug log
      const response = await api.get('/posts/getPosts');
      // Transform the response to match the expected format
      const rawPosts = response.data || [];

      console.log('Raw admin posts response:', rawPosts); // Debug log

      // Transform posts to match frontend expectations
      const posts = rawPosts.map(post => ({
        id: post._id,
        content: post.content,
        author: {
          id: post.userId._id,
          name: post.userId.name,
          email: post.userId.email
        },
        toxicity: post.toxicity,
        sentiment: post.sentiment,
        finalDecision: post.finalDecision,
        status: post.finalDecision, // Add status field for compatibility
        createdAt: post.createdAt,
        updatedAt: post.updatedAt || post.createdAt
      }));

      // Filter posts by status if provided
      let filteredPosts = posts;
      if (status) {
        filteredPosts = posts.filter(post => {
          if (status === 'allowed') return post.finalDecision === 'allowed';
          if (status === 'flagged') return post.finalDecision === 'flagged';
          if (status === 'removed') return post.finalDecision === 'removed';
          return true;
        });
      }

      console.log('Transformed admin posts:', filteredPosts); // Debug log

      // Calculate stats
      const stats = {
        total: posts.length,
        allowed: posts.filter(p => p.finalDecision === 'allowed').length,
        flagged: posts.filter(p => p.finalDecision === 'flagged').length,
        removed: posts.filter(p => p.finalDecision === 'removed').length,
      };

      return {
        data: {
          comments: filteredPosts,
          stats: stats
        }
      };
    } catch (error) {
      console.error('Error fetching admin posts:', error);
      throw error;
    }
  },

  // Create a new comment/post
  createComment: (content) => {
    return api.post('/posts/post', { content });
  },

  // Update post decision (admin only)
  updateCommentStatus: (commentId, status) => {
    // Map status to finalDecision format that backend expects
    const finalDecision = status === 'allowed' ? 'allowed' : 'removed';
    return api.post('/posts/updatePostDecision', {
      id: commentId,
      finalDecision: finalDecision
    });
  },

  // Allow a comment (admin only)
  allowComment: (commentId) => {
    return api.post('/posts/updatePostDecision', {
      id: commentId,
      finalDecision: 'allowed'
    });
  },

  // Remove a comment (admin only)
  removeComment: (commentId) => {
    return api.post('/posts/updatePostDecision', {
      id: commentId,
      finalDecision: 'removed'
    });
  },

  // Get comment by ID (if needed)
  getCommentById: (commentId) => {
    return api.get(`/posts/${commentId}`);
  },

  // Delete comment permanently (admin only)
  deleteComment: (commentId) => {
    return api.delete(`/posts/${commentId}`);
  },

  // Get comment statistics (admin only)
  getCommentStats: async () => {
    try {
      const response = await api.get('/posts/getPosts');
      const posts = response.data || [];

      const stats = {
        total: posts.length,
        allowed: posts.filter(p => p.finalDecision === 'allowed').length,
        flagged: posts.filter(p => p.finalDecision === 'flagged').length,
        removed: posts.filter(p => p.finalDecision === 'removed').length,
      };

      return { data: stats };
    } catch (error) {
      throw error;
    }
  },
};
