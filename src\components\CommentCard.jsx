import React from 'react';
import { User, Calendar, Heart, MessageCircle, Share } from 'lucide-react';

const CommentCard = ({ comment }) => {
  const getSentimentEmoji = (sentiment) => {
    if (!sentiment) return '😐';

    // Handle both string and object formats
    if (typeof sentiment === 'string') {
      if (sentiment.includes('positive')) return '😊';
      if (sentiment.includes('negative')) return '😞';
      return '😐';
    }

    const score = sentiment.score || 0;
    if (score > 0.1) return '😊';
    if (score < -0.1) return '😞';
    return '😐';
  };

  const getSentimentLabel = (sentiment) => {
    if (!sentiment) return 'Neutral';

    // Handle both string and object formats
    if (typeof sentiment === 'string') {
      // Capitalize first letter of each word
      return sentiment.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }

    const score = sentiment.score || 0;
    if (score > 0.1) return 'Positive';
    if (score < -0.1) return 'Negative';
    return 'Neutral';
  };

  const getToxicityColor = (toxicity) => {
    if (!toxicity) return 'text-gray-400';

    // Handle both string and object formats
    if (typeof toxicity === 'string') {
      if (toxicity.includes('toxic')) return 'text-red-400';
      return 'text-green-400';
    }

    const score = toxicity.score || 0;
    if (score > 0.7) return 'text-red-400';
    if (score > 0.4) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getToxicityLabel = (toxicity) => {
    if (!toxicity) return 'Clean';

    // Handle both string and object formats
    if (typeof toxicity === 'string') {
      // Capitalize first letter of each word
      return toxicity.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }

    const score = toxicity.score || 0;
    if (score > 0.7) return 'High Risk';
    if (score > 0.4) return 'Moderate';
    return 'Clean';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:bg-gray-800/70 transition-all duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div>
            <h4 className="font-semibold text-white">
              {comment.author?.name || 'Anonymous User'}
            </h4>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(comment.createdAt)}</span>
            </div>
          </div>
        </div>
        
        {/* Status Badge */}
        <div className="flex items-center space-x-2">
          <span className={`text-xs text-white px-2 py-1 rounded-full ${
            comment.finalDecision === 'allowed' ? 'bg-green-600' :
            comment.finalDecision === 'flagged' ? 'bg-yellow-600' :
            comment.finalDecision === 'removed' ? 'bg-red-600' : 'bg-gray-600'
          }`}>
            {comment.finalDecision || comment.status || 'pending'}
          </span>
        </div>
      </div>

      {/* Comment Content */}
      <div className="mb-4">
        <p className="text-gray-200 leading-relaxed">
          {comment.content}
        </p>
      </div>

      {/* AI Analysis */}
      <div className="flex items-center justify-between mb-4 p-3 bg-gray-900/50 rounded-lg">
        <div className="flex items-center space-x-4">
          {/* Sentiment */}
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getSentimentEmoji(comment.sentiment)}</span>
            <div>
              <div className="text-xs text-gray-400">Sentiment</div>
              <div className="text-sm text-white">
                {getSentimentLabel(comment.sentiment)}
              </div>
            </div>
          </div>
          
          {/* Toxicity */}
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              typeof comment.toxicity === 'string'
                ? (comment.toxicity.includes('toxic') ? 'bg-red-400' : 'bg-green-400')
                : (comment.toxicity?.score > 0.7 ? 'bg-red-400' :
                   comment.toxicity?.score > 0.4 ? 'bg-yellow-400' : 'bg-green-400')
            }`}></div>
            <div>
              <div className="text-xs text-gray-400">Toxicity</div>
              <div className={`text-sm ${getToxicityColor(comment.toxicity)}`}>
                {getToxicityLabel(comment.toxicity)}
              </div>
            </div>
          </div>
        </div>

        {/* Scores */}
        <div className="text-right">
          <div className="text-xs text-gray-400 mb-1">AI Analysis</div>
          <div className="flex space-x-3 text-xs">
            <span className="text-blue-400">
              S: {typeof comment.sentiment === 'string'
                ? comment.sentiment.slice(0, 8)
                : comment.sentiment?.score?.toFixed(2) || '0.00'}
            </span>
            <span className={getToxicityColor(comment.toxicity)}>
              T: {typeof comment.toxicity === 'string'
                ? comment.toxicity.slice(0, 8)
                : comment.toxicity?.score?.toFixed(2) || '0.00'}
            </span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-700">
        <div className="flex items-center space-x-4">
          <button className="flex items-center space-x-2 text-gray-400 hover:text-red-400 transition-colors">
            <Heart className="w-4 h-4" />
            <span className="text-sm">Like</span>
          </button>
          
          <button className="flex items-center space-x-2 text-gray-400 hover:text-blue-400 transition-colors">
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm">Reply</span>
          </button>
          
          <button className="flex items-center space-x-2 text-gray-400 hover:text-green-400 transition-colors">
            <Share className="w-4 h-4" />
            <span className="text-sm">Share</span>
          </button>
        </div>
        
        <div className="text-xs text-gray-500">
          ID: {comment.id?.slice(-8) || 'unknown'}
        </div>
      </div>
    </div>
  );
};

export default CommentCard;
