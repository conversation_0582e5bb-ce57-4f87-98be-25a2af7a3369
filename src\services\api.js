import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api', // Use proxy in development
  timeout: 10000,
  withCredentials: true, // Enable sending cookies with requests
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor (cookies are automatically sent with withCredentials: true)
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.baseURL + config.url); // Debug log
    console.log('API Request - Using cookies for authentication'); // Debug log
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response Success:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error Details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });

    if (error.response?.status === 401) {
      console.log('401 Unauthorized - Cookie expired or invalid, redirecting to login'); // Debug log
      // Cookie expired or invalid, clear user data and redirect
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Test function to check backend connectivity
export const testBackendConnection = async () => {
  try {
    console.log('Testing backend connection...');
    const response = await api.get('/health'); // Try a simple health check
    console.log('Backend connection successful:', response.data);
    return true;
  } catch (error) {
    console.error('Backend connection failed:', error.message);

    // Try a direct fetch to see if it's a CORS issue
    try {
      const directResponse = await fetch('http://localhost:5000/api/health');
      console.log('Direct fetch status:', directResponse.status);
      if (directResponse.ok) {
        console.log('Direct fetch successful - this might be a CORS issue');
      }
    } catch (fetchError) {
      console.error('Direct fetch also failed:', fetchError.message);
    }

    return false;
  }
};

export default api;
